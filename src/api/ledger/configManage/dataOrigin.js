import request from "@/utils/request";
import { queryLog } from "@/api/common.js";

/**
 * 数据来源配置管理API接口
 */
export default {
  /**
   * 列表查询
   * @param {Object} data - 查询参数
   * @param {string} data.dataSourceChannel - 数据源渠道
   * @param {string} data.dingTalkWorkOrderName - 钉钉工单名称
   * @param {number} data.needPush - 维保通是否需要推送 (0:否, 1:是)
   * @param {number} data.status - 状态 (0:禁用, 1:启用)
   * @param {number} data.pageNum - 页码
   * @param {number} data.pageSize - 每页大小
   * @returns {Promise} 返回查询结果
   */
  getTableData(data) {
    return request({
      url: "/ledger/dataOrigin/queryList",
      method: "post",
      data: data,
    });
  },

  /**
   * 新增/编辑数据来源配置
   * @param {Object} data - 配置数据
   * @param {string} data.dataSourceChannel - 数据源渠道 (钉钉/运管中间表)
   * @param {string} data.dingTalkWorkOrderId - 钉钉工单ID
   * @param {string} data.dingTalkWorkOrderName - 钉钉工单名称
   * @param {string} data.pushCondition - 推送的前置条件
   * @param {string} data.nodeName - 节点名称
   * @param {string} data.tableName - 表名
   * @param {string} data.precondition - 前置条件
   * @param {string} data.syncFieldNames - 需同步的字段名称
   * @param {Object} data.dataMappingRelation - 数据映射关系
   * @param {number} data.needPush - 维保通是否需要推送 (0:否, 1:是)
   * @param {string} data.pushPrecondition - 外传的前置条件
   * @param {string} data.pushFieldNames - 需推送的字段名称
   * @param {string} data.middleTableFieldDisposal - 中间表侧字段处置
   * @param {number} data.status - 状态 (0:禁用, 1:启用)
   * @returns {Promise} 返回操作结果
   */
  update(data) {
    return request({
      url: "/ledger/dataOrigin/save",
      method: "post",
      data: data,
    });
  },

  /**
   * 删除数据来源配置
   * @param {Object} params - 删除参数
   * @param {string} params.id - 配置ID
   * @returns {Promise} 返回删除结果
   */
  deleteData(params) {
    return request({
      url: "/ledger/dataOrigin/remove",
      method: "get",
      params: params,
    });
  },

  /**
   * 查询操作日志
   * @param {Object} params - 查询参数
   * @param {string} params.id - 配置ID
   * @returns {Promise} 返回日志列表
   */
  queryLog(params) {
    return queryLog({
      ...params,
      businessType: "dataOrigin", // 业务类型标识
    });
  },

  /**
   * 获取详情
   * @param {Object} params - 查询参数
   * @param {string} params.id - 配置ID
   * @returns {Promise} 返回详情数据
   */
  getDetail(params) {
    return request({
      url: "/ledger/dataOrigin/detail",
      method: "get",
      params: params,
    });
  },

  /**
   * 状态切换
   * @param {Object} data - 状态切换参数
   * @param {string} data.id - 配置ID
   * @param {number} data.status - 状态 (0:禁用, 1:启用)
   * @returns {Promise} 返回操作结果
   */
  changeStatus(data) {
    return request({
      url: "/ledger/dataOrigin/changeStatus",
      method: "post",
      data: data,
    });
  },

  /**
   * 导出数据
   * @param {Object} params - 导出参数
   * @returns {Promise} 返回导出结果
   */
  export(params) {
    return request({
      url: "/ledger/dataOrigin/export",
      method: "post",
      data: params,
      responseType: "blob",
    });
  },
};
