//服务商管理-施工队
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="rowAdd"
          v-has-permi="['constructionTeam:manage:add']"
          >新增</el-button
        >
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['constructionTeam:manage:import']"
          >导入施工质保有效期</el-button
        >
        <el-button
          type="primary"
          @click="handleSubmit"
          v-has-permi="['constructionTeam:manage:refresh']"
          >刷新施工队信息</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <!-- <el-button
          type="primary"
          icon="el-icon-download"
          @click.stop="handleExport"
          v-has-permi="['maintenance:timeConfig:export']"
          >导出
        </el-button> -->
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleSubmit"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>

      <template #statusChange="{ row }"
        ><el-switch
          v-model="row.status"
          active-value="0"
          inactive-value="1"
          @change="handleStatusChange(row)"
          :disabled="!checkPermission(['constructionTeam:manage:status'])"
        >
        </el-switch
      ></template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'account'">
          <el-button @click="submitAccount(row, false)">解绑</el-button>
          <el-button @click="submitAccount(row, true)" type="primary"
            >绑定</el-button
          >
        </div>
        <div v-if="crudOperationType === 'manager'">
          <el-button @click="submitManager(row, false)"
            >解绑所有项目经理
          </el-button>
          <el-button @click="submitManager(row, true)" type="primary"
            >绑定
          </el-button>
        </div>
      </template>
      <template #manager="{ row, operationType }">
        <el-form
          :model="item"
          :ref="'addForm_' + index"
          v-for="(item, index) in addForm"
          :key="index"
          :rules="rules"
          label-width="150px"
        >
          <el-row style="display: flex;align-items:center">
            <el-col :span="20">
              <el-form-item label="选择项目经理:" prop="userId">
                <el-select
                  v-model="item.userId"
                  filterable
                  clearable
                  placeholder="请选择项目经理"
                  style="width: 100%;"
                >
                  <el-option
                    v-for="x in managerOptions"
                    :key="x.userId"
                    :label="
                      x.nickName +
                        '-' +
                        x.userName +
                        '-' +
                        (x.phonenumber || '')
                    "
                    :value="x.userId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="3" :offset="1">
              <el-button
                type="primary"
                circle
                icon="el-icon-plus"
                @click="addItem"
                v-if="index === 0"
                style="margin-bottom: 18px;"
              />
              <el-button
                type="primary"
                circle
                icon="el-icon-minus"
                style="background:red;border:1px solid red;margin-bottom: 18px;"
                @click="removeItem(index)"
                v-else
              />
            </el-col>
          </el-row>
        </el-form>
      </template>
    </BuseCrud>
    <TeamDetail
      :type="operationType"
      :title="
        operationType === 'add'
          ? '新建施工队'
          : operationType === 'update'
          ? '编辑施工队'
          : '施工队详情'
      "
      :constructId="constructId"
      :applyNo="applyNo"
      v-if="detailVisible"
      :visible.sync="detailVisible"
      @update-list="loadData"
    ></TeamDetail>
    <BatchUpload
      @uploadSuccess="loadData"
      ref="batchUpload"
      title="批量导入施工质保有效期"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import { listUser } from "@/api/system/user.js";
import api from "@/api/constructionTeam/index.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import TeamDetail from "./components/detail.vue";
import BatchUpload from "@/components/BatchUpload/index.vue";
import { listAllUser } from "@/api/common.js";
export default {
  name: "constructionTeam",
  components: { TeamDetail, BatchUpload },
  mixins: [exportMixin],
  data() {
    return {
      uploadObj: {
        api: "/export/report/construct/import",
        url: "/charging-maintenance-ui/static/施工质保有效期导入模板.xlsx",
        extraData: {},
      },
      constructId: "",
      applyNo: "",
      operationType: "update",
      detailVisible: false,
      addForm: [{ userId: "" }],
      rules: {
        userId: [
          { required: true, message: "项目经理不能为空！", trigger: "change" },
          { validator: this.validateUserId, trigger: "change" },
        ],
      },
      businessTypeOptions: [],
      orderTypeOptions: [],
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "constructId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],
      tableColumn: [
        {
          field: "constructName",
          title: "施工队名称",
          width: 200,
        },
        {
          field: "constructCode",
          title: "施工队编码",
          width: 100,
        },
        {
          field: "contactName",
          title: "施工队联系人",
          width: 120,
        },
        {
          field: "contactPhone",
          title: "施工队联系人电话",
          width: 150,
        },
        {
          field: "constructManager",
          title: "施工队项目经理",
          width: 150,
        },
        {
          field: "applyNo",
          title: "运管采购框架协议申请单号",
          width: 180,
          //   width: 250,
        },
        {
          field: "enterType",
          title: "录入方式",
          width: 150,
          formatter: ({ cellValue }) => {
            return cellValue == "1"
              ? "运营同步"
              : cellValue == "2"
              ? "自建"
              : cellValue;
          },
        },
        {
          field: "procotolTypeName",
          title: "施工队类别",
          width: 150,
        },
        {
          field: "certLife",
          title: "施工资质截止日期",
          width: 150,
          slots: {
            default: ({ row }) => {
              return [
                <span
                  style={{ color: row.certLifeAgingFlag == "1" ? "red" : "" }}
                >
                  {row.certLife}
                </span>,
              ];
            },
          },
        },
        {
          field: "principalName",
          title: "施工队负责人",
          width: 150,
        },
        {
          field: "principalAccount",
          title: "施工队负责人账号",
          width: 150,
        },
        {
          field: "warrantyYear",
          title: "施工队质保有效期",
          width: 150,
          formatter: ({ cellValue }) => {
            return cellValue || cellValue == 0 ? cellValue + "年" : "";
          },
        },
        // {
        //   field: "principalAccount",
        //   title: "综合评价",
        //   width: 100,
        // },
        {
          field: "status",
          title: "状态",
          width: 100,
          slots: {
            default: "statusChange",
          },
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      accountOptions: [],
      managerOptions: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        //默认显示筛选项的个数
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        //筛选控件配置
        config: [
          {
            field: "constructName",
            element: "el-input",
            title: "施工队名称",
          },
          {
            field: "constructCode",
            title: "施工队编码",
          },
          {
            field: "principalName",
            title: "施工队负责人",
          },
          {
            field: "procotolTypeName",
            title: "服务商类别",
            element: "el-select",
            props: {
              options: [{ label: "建筑安装", value: "建筑安装" }],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        addTitle: "新建施工队",
        editBtn: false,
        editTitle: "",
        delBtn: checkPermission(["constructionTeam:manage:delete"]),
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [
          {
            field: "principalAccount",
            title: "施工队负责人账号",
            element: "el-select",
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.accountOptions,
              //   optionLabel: "dictLabel",
              //   optionValue: "dictValue",
              filterable: true,
            },
            rules: [
              {
                required: true,
                message: "请选择施工队负责人账号",
              },
            ],
          },
        ],
        customOperationTypes: [
          {
            title: "编辑",
            typeName: "edit",
            slotName: "detail",
            modalTitle: "编辑施工队",
            event: (row) => {
              return this.handleEdit(row, "update");
            },
            condition: (row) => {
              return checkPermission(["constructionTeam:manage:edit"]);
            },
          },
          {
            title: "施工队负责人账号",
            typeName: "account",
            modalTitle: "绑定施工队负责人",
            event: (row) => {
              return this.handleAccount(row);
            },
            condition: (row) => {
              return checkPermission(["constructionTeam:manage:account"]);
            },
          },
          {
            title: "绑定项目经理",
            modalTitle: "绑定施工队项目经理",
            typeName: "manager",
            showForm: false,
            slotName: "manager",
            event: (row) => {
              return this.handleManager(row);
            },
            condition: (row) => {
              return checkPermission(["constructionTeam:manage:manager"]);
            },
          },
          {
            title: "详情",
            typeName: "detail",
            slotName: "detail",
            modalTitle: "施工队详情",
            event: (row) => {
              return this.handleEdit(row, "detail");
            },
            condition: (row) => {
              return checkPermission(["constructionTeam:manage:detail"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "150px",
        },
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
  },
  mounted() {
    this.getUserList();
    this.getManagerOptions();
    Promise.all([
      //   this.getDicts("order_business_type").then((response) => {
      //     this.businessTypeOptions = response.data;
      //   }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.loadData();
        });
      }, 500);
    });
  },
  methods: {
    checkPermission,
    handleBatchAdd() {
      this.$refs.batchUpload.open();
    },
    rowAdd() {
      this.handleEdit({}, "add");
    },
    handleSubmit() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    handleEdit(row, type = "update") {
      this.operationType = type;
      this.detailVisible = true;
      this.constructId = row.constructId;
      this.applyNo = row.applyNo;
    },
    validateUserId(rule, value, callback) {
      const form = this.addForm.filter((x) => x.userId == value);
      if (form?.length > 1) {
        callback(new Error("项目经理不允许重复！"));
      }
      callback();
    },
    getManagerOptions() {
      api.listRoleAccount({ roleKey: "consTeamProjectManager" }).then((res) => {
        this.managerOptions = res.data;
      });
    },
    getUserList() {
      listAllUser({ status: "0" }).then((res) => {
        this.accountOptions = res.data?.map((x) => {
          return {
            ...x,
            label: x.nickName + "-" + x.userName + "-" + (x.phonenumber || ""),
            value: x.userName,
          };
        });
      });
    },
    // 绑定施工队负责人
    handleAccount(row) {
      this.$refs.crud.switchModalView(true, "account", {
        ...initParams(this.modalConfig.formConfig),
        ...row,
      });
    },
    submitAccount(row, flag) {
      if (flag) {
        this.$refs.crud.$refs.modalView.$refs.dynamicForm.validate((valid) => {
          if (!valid) return;

          const params = {
            constructId: row.constructId,
            constructCode: row.constructCode,
            principalName: this.accountOptions?.find(
              (x) => x.userName == row.principalAccount
            )?.nickName,
            principalAccount: row.principalAccount,
          };
          api.submitAccount(params).then((res) => {
            if (res?.code == "10000") {
              this.$message.success("绑定成功");
              this.$refs.crud.switchModalView(false);
              this.loadData();
            }
          });
        });
      } else {
        const params = {
          constructId: row.constructId,
          constructCode: row.constructCode,
        };
        api.submitUnbindAccount(params).then((res) => {
          if (res?.code == "10000") {
            this.$refs.crud.switchModalView(false);
            this.$message.success("解绑成功");
            this.loadData();
          }
        });
      }
    },
    //绑定施工队项目经理
    handleManager(row) {
      api.bindManagerList({ constructId: row.constructId }).then((res) => {
        if (res.data?.length > 0) {
          this.originalManagerList = res.data;
          this.addForm = res.data?.map((x) => {
            return { userId: x.userId };
          });
        } else {
          this.addForm = [{ userId: "" }];
        }
        this.$refs.crud.switchModalView(true, "manager", { ...row });
      });
    },
    submitManager(row, flag) {
      let validCount = 0;
      flag &&
        this.addForm.map((item, index) => {
          this.$refs["addForm_" + index][0].validate(async (valid) => {
            if (valid) {
              validCount++;
            }
          });
        });
      if (validCount == this.addForm.length || !flag) {
        const text = flag ? "是否确认绑定？" : "是否确认全部解绑？";
        this.$confirm(text, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            console.log(this.addForm, "---");
            const form = this.addForm.map((item) => {
              let obj =
                this.managerOptions?.find((x) => x.userId == item.userId) ||
                this.originalManagerList?.find((x) => x.userId == item.userId);
              return {
                ...obj,
                constructId: row.constructId,
              };
            });
            const method = flag ? "submitManager" : "unbindAllManager";
            api[method](flag ? form : { constructId: row.constructId }).then(
              (res) => {
                if (res?.code == "10000") {
                  this.$message.success("提交成功");
                  this.$refs.crud.switchModalView(false);
                  this.loadData();
                }
              }
            );
          })
          .catch(() => {});
      }
    },
    addItem() {
      // if (this.addForm.length === 10) {
      //   this.$message.warning("最多添加10组！");
      //   return;
      // }
      this.addForm.push({ mobile: "", dateRange: [], reason: "" });
      //   }
    },
    removeItem(index) {
      this.addForm.splice(index, 1);
    },
    //状态切换
    handleStatusChange(row) {
      // if (row.status == "0") {
      //   // this.$message.warning("自动派单的站点不能为空，请配置站点！");
      //   row.status = row.status == 1 ? 0 : 1;
      //   return;
      // }
      if (row.status == "1") {
        this.$confirm("是否确认停用？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then((res) => {
            this.updateStatus(row);
          })
          .catch(() => {
            row.status = row.status == "1" ? "0" : "1";
          });
      } else {
        this.updateStatus(row);
      }
    },
    updateStatus(row) {
      const text = row.status == "0" ? "启用" : "停用";
      //   const { updateTime, createTime, ...params } = row;
      api
        .update({ constructId: row.constructId, status: row.status })
        .then((res) => {
          if (res.code == "10000") {
            this.msgSuccess(text + "成功");
            this.loadData();
          } else {
            row.status = row.status == "1" ? "0" : "1";
          }
        })
        .catch(function() {
          row.status = row.status == "1" ? "0" : "1";
        });
    },

    // handleExport() {
    //   const params = {
    //     ...this.params,
    //     pageNum: this.tablePage.currentPage,
    //     pageSize: this.tablePage.pageSize,
    //   };
    //   this.handleCommonExport(api.export, params);
    // },

    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.loading = true;
      const res = await api.getTableData(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      console.log(crudOperationType, formParams, "提交");
      //   const { updateTime, createTime, ...params } = formParams;
      //   api.update(params).then((res) => {
      //     if (res.code === "10000") {
      //       this.$message.success("提交成功");
      //       this.loadData();
      //     }
      //   });
    },

    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          constructId: row.constructId,
        };
        api.deleteData(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
