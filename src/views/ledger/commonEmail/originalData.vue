<!-- 邮件原始数据 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <div style="display: flex;justify-content: flex-end;width: 100%;">
          <el-button
            type="text"
            @click="handleCustom"
            style="margin-right: 10px;"
            :loading="btnLoading"
            >{{
              isCustomColumn ? "已开启偏好设置" : "将自定义列设为偏好"
            }}</el-button
          >
        </div>
      </template>
      <template #menu="{row,tableRow}">
        <el-button
          v-for="item in outBtnArr"
          :key="item.typeName"
          type="text"
          size="medium"
          @click="item.event(row)"
          v-show="!!item.condition(row)"
          >{{ item.title }}</el-button
        >
        <el-dropdown
          @command="(command) => handleCommand(command, row)"
          class="ml10"
        >
          <el-button type="text" size="medium">
            快捷操作<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              :command="item.typeName"
              v-for="(item, index) in btnArr"
              :key="index"
              v-show="!!item.condition(row)"
            >
              <el-button type="text" size="medium">
                {{ item.title }}
              </el-button>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['commonEmail:originalData:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            :loading="loading"
            >查询
          </el-button>
          <el-button
            icon="el-icon-refresh"
            @click.stop="handleReset"
            :loading="loading"
            >重置
          </el-button>
          <el-button
            type="text"
            @click="handleFilterCustom"
            style="margin-left: 10px;"
            :loading="filterBtnLoading"
            >{{ isFilterCustom ? "已开启偏好设置" : "设为偏好" }}</el-button
          >
          <FilterPreference
            v-model="selectedFilterFields"
            :filterOptions="filterOptions.config"
            :defaultFields="defaultFilterFields"
            :loading="filterBtnLoading"
            @change="handleFilterFieldsChange"
          />
        </div>
      </template>
      <template #file="{row, operationType}">
        <FileTable
          :fileList="row.fileList"
          :fileOptions="{ url: 'storePath', name: 'docName' }"
        ></FileTable>
      </template>
      <template #sign="{ row }">
        <div v-html="row.senderSign" v-if="row.senderSign"></div>
        <el-empty v-else></el-empty>
      </template>
      <template #modalFooter="{ crudOperationType }">
        <div
          v-if="crudOperationType === 'file' || crudOperationType === 'sign'"
        ></div>
      </template>
    </BuseCrud>
    <ConfigDrawer ref="configDrawer" @close="loadData"></ConfigDrawer>
    <RecheckDrawer ref="recheckDrawer" @close="loadData"></RecheckDrawer>
    <DifferenceDrawer
      ref="differenceDrawer"
      @close="loadData"
    ></DifferenceDrawer>
    <ReplyEmailDrawer
      ref="replyEmailDialog"
      @close="loadData"
    ></ReplyEmailDrawer>
    <el-drawer
      :title="resultType === 'recheck' ? '活动复核结果' : '活动配置结果'"
      :visible.sync="resultVisible"
      size="80%"
    >
      <ResultDetail
        v-if="resultVisible"
        ref="resultDetail"
        :mailDataId="mailDataId"
        :resultType="resultType"
      ></ResultDetail>
    </el-drawer>
    <el-drawer :title="mailTitle" :visible.sync="emailVisible" size="80%">
      <div v-if="mailTitle === '邮件正文'">
        <DynamicForm
          :config="mailConfig"
          preview
          :params="mailObj"
          :defaultColSpan="24"
          labelPosition="right"
          labelWidth="100px"
        >
          <template #copy="{item,params}">
            <span>{{ params[item.field] }} </span>
            <i
              class="el-icon-document-copy pointer-icon"
              @click="copyToClipboard(params[item.field])"
            ></i>
          </template>
        </DynamicForm>
        <div style="display: flex;justify-content: flex-end;">
          <el-button type="text" @click="copyToClipboard(mailObj.mailContent)">
            <i class="el-icon-document-copy pointer-icon"></i>
            复制html
          </el-button>
          <el-button type="text" @click="copyToClipboard(mailObj.contentText)">
            <i class="el-icon-document-copy pointer-icon"></i>
            复制正文
          </el-button>
        </div>
      </div>
      <div
        v-html="mailHtmlText"
        v-if="mailHtmlText"
        style="border:1px solid #ccc;padding: 10px;"
      ></div>
      <el-empty v-else></el-empty>
    </el-drawer>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/ledger/commonEmail.js";
import { initParams } from "@/utils/buse.js";
import * as comApi from "@/api/common.js";
import exportMixin from "@/mixin/export.js";
import ConfigDrawer from "./components/configDrawer.vue";
import RecheckDrawer from "./components/recheckDrawer.vue";
import DifferenceDrawer from "./components/differenceDrawer.vue";
import ResultDetail from "./components/resultDetail.vue";
import ReplyEmailDrawer from "./components/replyEmailDrawer.vue";
import FileTable from "@/components/PreviewFiles/fileTable.vue";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { copyToClipboard } from "@/utils/index.js";
import FilterPreference from "./components/FilterPreference.vue";
export default {
  name: "ledgerList",
  components: {
    ConfigDrawer,
    RecheckDrawer,
    DifferenceDrawer,
    ResultDetail,
    ReplyEmailDrawer,
    FileTable,
    FilterPreference,
  },
  mixins: [exportMixin],
  props: {
    searchParams: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      mailObj: {},
      mailTitle: "邮件正文",
      // Preference data
      storeData: {},
      filterStoreData: {},
      btnLoading: false,
      filterBtnLoading: false,
      restorePromise: null,
      filterRestorePromise: null,
      isCustomColumn: false,
      isFilterCustom: false,
      isPreferenceInitialized: false, // 标记表格偏好设置是否已初始化
      selectedFilterFields: [],
      defaultFilterFields: [
        "obscureParams",
        "receiveTime",
        "activityType",
        "receiveOrderStatus",
        "receiveOrderUserName",
        "receiveOrderTime",
        "sender",
        "configStatus",
      ],
      //buse参数-s
      loading: false,
      tableData: [],
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "accept",
      //buse参数-e
      cancelTypeOptions: [],
      activityTypeOptions: [],
      receiveOrderStatusOptions: [],
      configStatusOptions: [],
      workTimeTypeOptions: [],
      params: {},
      emailVisible: false,
      mailHtmlText: "",
      senderSign: "",
      fileList: [],
      resultVisible: false,
      mailDataId: "",
      resultType: "config",
      mailSourceOptions: [],
    };
  },
  watch: {
    searchParams: {
      handler(val) {
        console.log("searchParams", val);
        this.params = { ...val };
      },
    },
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };

    this.selectedFilterFields = [...this.defaultFilterFields];
    // 初始化表格列偏好设置
    this.initTablePreference();
    // 初始化筛选条件偏好设置
    this.initFilterPreference();

    this.getDicts("cancel_type").then((response) => {
      this.cancelTypeOptions = response.data;
    });
    this.getDicts("activity_type").then((response) => {
      this.activityTypeOptions = response.data;
    });
    this.getDicts("receive_status").then((response) => {
      this.receiveOrderStatusOptions = response.data;
    });
    this.getDicts("config_status").then((response) => {
      this.configStatusOptions = response.data;
    });
    api.getMailSource({}).then((res) => {
      this.mailSourceOptions = res.data?.map((x) => {
        return { value: x, label: x };
      });
    });
    // this.loadData();
  },
  mounted() {
    // 初始化表格列偏好设置
    // this.initColumnPreference();
  },

  methods: {
    copyToClipboard,
    checkPermission,
    handleCommand(command, row) {
      this.btnArr?.find((x) => x.typeName == command)?.event(row);
    },
    handleSignView(row) {
      this.senderSign = row.senderSign;
      this.$refs.crud.switchModalView(true, "sign", {
        senderSign: row.senderSign,
      });
    },
    //详情
    jumpToDetail(row) {
      this.$router.push({
        path: "/ledger/commonEmail/detail",
        query: {
          mailId: row.mailId,
          messageId: row.messageId,
        },
      });
    },
    //查看配置结果
    handleConfigResult(row) {
      this.mailDataId = row.mailId;
      this.resultVisible = true;
      this.resultType = "config";
    },
    //查看复核结果
    handleRecheckResult(row) {
      this.mailDataId = row.mailId;
      this.resultVisible = true;
      this.resultType = "recheck";
    },
    //查看附件
    handleFileTable(row) {
      this.$refs.crud.switchModalView(true, "file", {
        fileList: row.attachment,
      });
    },
    // 配置
    handleConfig(row) {
      this.$router.push({
        path: "/ledger/commonEmail/config",
        query: {
          mailId: row.mailId,
          messageId: row.messageId,
        },
      });
    },
    //复核
    handleRecheck(row) {
      this.$router.push({
        path: "/ledger/commonEmail/recheck",
        query: {
          mailId: row.mailId,
        },
      });
    },
    //差异
    handleDifference(row) {
      this.$refs.differenceDrawer.open(row);
    },
    //发邮件
    handleReplyEmail(row) {
      this.$refs.replyEmailDialog.open(row);
    },
    //接单
    handleAccept(row) {
      this.operationType = "accept";
      this.$refs.crud.switchModalView(true, "accept", {
        ...initParams(this.modalConfig.formConfig),
        mailId: row.mailId,
      });
    },
    //作废
    handleCancel(row) {
      this.operationType = "cancel";
      this.$refs.crud.switchModalView(true, "cancel", {
        ...initParams(this.modalConfig.formConfig),
        mailId: row.mailId,
      });
    },
    //活动类别
    handleActivityType(row) {
      this.operationType = "activityType";
      this.$refs.crud.switchModalView(true, "activityType", {
        ...initParams(this.modalConfig.formConfig),
        mailId: row.mailId,
      });
    },
    handleRemark(row) {
      this.operationType = "remark";
      this.$refs.crud.switchModalView(true, "remark", {
        ...initParams(this.modalConfig.formConfig),
        mailId: row.mailId,
      });
    },
    // 打开邮件正文抽屉
    handleEmailDrawer(row, field, title) {
      this.mailTitle = title;
      this.mailHtmlText = row[field];
      this.mailObj = row;
      this.emailVisible = true;
    },
    handleExport() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "receiveTime",
          title: "收件时间",
          startFieldName: "receiveStartTime",
          endFieldName: "receiveEndTime",
        },
        {
          field: "receiveOrderTime",
          title: "接单时间",
          startFieldName: "receiveOrderStartTime",
          endFieldName: "receiveOrderEndTime",
        },
        {
          field: "configTime",
          title: "配置时间",
          startFieldName: "configStartTime",
          endFieldName: "configEndTime",
        },
        {
          field: "reviewTime",
          title: "复核时间",
          startFieldName: "reviewStartTime",
          endFieldName: "reviewEndTime",
        },
        {
          field: "sendTime",
          title: "发送时间",
          startFieldName: "sendStartTime",
          endFieldName: "sendEndTime",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.getTableData(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.orderTypeOptions = [];
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    // 处理表格列偏好设置
    async handleCustom() {
      // 获取当前表格的列配置状态
      const currentStoreData = this.$refs.crud
        .getVxeTableRef()
        ?.getCustomStoreData();
      const method = this.isCustomColumn
        ? "cancelCustomColumn"
        : "saveCustomColumn";
      this.btnLoading = true;

      const res = await comApi[method]({
        menuFlag: "emailOriginalData",
        preferenceJson: JSON.stringify(currentStoreData),
      }).catch(() => {
        this.btnLoading = false;
      });
      this.btnLoading = false;
      const text = this.isCustomColumn ? "偏好设置已取消" : "偏好设置已开启";
      if (res.code === "10000") {
        this.isCustomColumn = !this.isCustomColumn;
        this.$message.success(text);
        this.restorePromise = null;

        // 无论开启还是取消偏好设置，都保存当前的表格列配置状态
        // 这样可以确保用户当前调整的表格列配置不会丢失
        this.storeData = currentStoreData;

        // 不重置 isPreferenceInitialized 标志，保持已初始化状态
        // 这样可以防止 restoreStore 重新从服务器获取旧的偏好设置
      }
    },
    // 初始化表格列偏好设置
    async initTablePreference() {
      try {
        const res = await comApi.queryCustomColumn({
          menuFlag: "emailOriginalData",
        });
        if (res.code === "10000") {
          this.isCustomColumn = res.data.preferenceFlag === "Y";
          this.isPreferenceInitialized = true;
          if (this.isCustomColumn && res.data.preferenceJson) {
            this.storeData = JSON.parse(res.data.preferenceJson);
          }
        }
      } catch (error) {
        console.error("Failed to load table preferences:", error);
        this.isPreferenceInitialized = true; // 即使失败也标记为已初始化
      }
    },
    // 处理筛选条件偏好设置
    async handleFilterCustom() {
      const method = this.isFilterCustom
        ? "cancelCustomColumn"
        : "saveCustomColumn";
      this.filterBtnLoading = true;
      const res = await comApi[method]({
        menuFlag: "emailOriginalDataFilter", // 使用不同的menuFlag区分筛选条件偏好设置
        preferenceJson: JSON.stringify(this.selectedFilterFields),
      }).catch(() => {
        this.filterBtnLoading = false;
      });
      this.filterBtnLoading = false;
      const text = this.isFilterCustom
        ? "筛选条件偏好设置已取消"
        : "筛选条件偏好设置已开启";
      if (res.code === "10000") {
        this.isFilterCustom = !this.isFilterCustom;
        this.$message.success(text);
        this.filterRestorePromise = null;
      }
    },
    // 处理筛选字段变更
    async handleFilterFieldsChange(fields) {
      this.selectedFilterFields = fields;
      if (this.isFilterCustom) {
        const res = await comApi.saveCustomColumn({
          preferenceJson: JSON.stringify(fields),
          menuFlag: "emailOriginalDataFilter",
        });
        if (res.code === "10000") {
          this.$message.success("保存成功");
        }
      }
    },
    // 初始化表格列偏好设置
    async initColumnPreference() {
      try {
        const res = await comApi.queryCustomColumn({
          menuFlag: "emailOriginalData",
        });
        this.isCustomColumn = res.data.preferenceFlag === "Y";
      } catch (error) {
        console.error("Failed to load column preferences:", error);
      }
    },

    // 初始化筛选条件偏好设置
    async initFilterPreference() {
      try {
        const res = await comApi.queryCustomColumn({
          menuFlag: "emailOriginalDataFilter", // 使用不同的menuFlag区分筛选条件偏好设置
        });
        if (res.code === "10000") {
          this.isFilterCustom = res.data.preferenceFlag === "Y";
          if (this.isFilterCustom && res.data.preferenceJson) {
            this.selectedFilterFields = JSON.parse(res.data.preferenceJson);
          }
        }
      } catch (error) {
        console.error("Failed to load filter preferences:", error);
      }
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");
      // crudOperationType:accept/remark/cancel/activityType

      return new Promise(async (resolve) => {
        if (crudOperationType === "accept" || crudOperationType === "cancel") {
          this.$confirm(`是否确定提交？`, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(async () => {
              const res = await api[crudOperationType](params);
              if (res?.code === "10000") {
                this.$message.success("提交成功");
                this.loadData();
                resolve(true);
              } else {
                resolve(false);
              }
            })
            .catch(() => {
              resolve(false);
            });
        } else {
          const res = await api[crudOperationType](params);
          if (res?.code === "10000") {
            this.$message.success("提交成功");
            this.loadData();
            resolve(true);
          } else {
            resolve(false);
          }
        }
      });
    },
    getTagColor(status) {
      const arr = [
        { status: "已接单", fontColor: "#459a7e" },
        { status: "未接单", fontColor: "#e83425" },
        { status: "已作废", fontColor: "#909399" },
        { status: "未配置", fontColor: "#e6a23c" },
        { status: "已配置", fontColor: "#459a7e" },
        { status: "已接单-配置", fontColor: "#459a7e" },
        { status: "已接单-复核", fontColor: "#459a7e" },
        { status: "已复核", fontColor: "#2a6198" },
        { status: "复核驳回", fontColor: "#e83425" },
      ];
      return arr.find((x) => x.status == status)?.fontColor || "#606266";
    },
  },
  computed: {
    tableProps() {
      const that = this;
      return {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
        customConfig: {
          storage: true,
          async restoreStore({ id, type, storeData }) {
            console.log("restoreStore", id, type, storeData);
            // 如果已经初始化过偏好设置，直接返回当前存储的数据
            // 这样可以确保用户调整的表格列配置不会被重置
            if (that.isPreferenceInitialized) {
              return that.storeData || {};
            }
            // 如果未发起过请求，则创建 Promise（仅在页面初始化时执行）
            if (!that.restorePromise) {
              that.restorePromise = (async () => {
                try {
                  const res = await comApi.queryCustomColumn({
                    menuFlag: "emailOriginalData",
                  });
                  that.isCustomColumn = res.data.preferenceFlag === "Y";
                  that.isPreferenceInitialized = true; // 标记已初始化
                  // 根据服务端数据或默认值返回
                  const preferenceData = that.isCustomColumn
                    ? JSON.parse(res.data.preferenceJson)
                    : {};
                  // 保存偏好设置数据
                  that.storeData = preferenceData;
                  return preferenceData;
                } catch (e) {
                  that.isPreferenceInitialized = true; // 即使失败也标记为已初始化
                  that.storeData = {}; // 确保有默认值
                  return {}; // 失败返回空
                }
              })();
            }
            // 返回缓存的 Promise
            return that.restorePromise;
          },
          updateStore({ id, type, storeData }) {
            // 原有保存逻辑不变
            return new Promise(async (resolve) => {
              that.storeData = storeData;
              if (that.isCustomColumn) {
                const res = await comApi.saveCustomColumn({
                  preferenceJson: JSON.stringify(storeData),
                  menuFlag: "emailOriginalData",
                });
                if (res.code === "10000") {
                  that.$message.success("保存成功");
                }
              }
              resolve();
            });
          },
        },
        id: "emailOriginalDataId",
      };
    },
    mailConfig() {
      return [
        { field: "mailSubject", title: "邮件标题：" },
        { field: "sender", title: "发件人：", previewSlot: "copy" },
        { field: "ccTo", title: "抄送人：", previewSlot: "copy" },
        { field: "sentTime", title: "发件时间：" },
      ];
    },
    tableColumn() {
      return [
        {
          field: "mailSource",
          title: "邮件来源",
          width: 100,
        },
        {
          field: "mailSubject",
          title: "邮件标题",
          width: 180,
        },
        {
          field: "mailContent",
          title: "邮件正文",
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <el-button
                  type="text"
                  size="large"
                  on={{
                    click: () =>
                      this.handleEmailDrawer(row, "mailContent", "邮件正文"),
                  }}
                >
                  查看
                </el-button>,
              ];
            },
          },
        },
        {
          field: "file",
          title: "附件",
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <el-button
                  type="text"
                  size="large"
                  on={{
                    click: () => this.handleFileTable(row),
                  }}
                >
                  查看
                </el-button>,
              ];
            },
          },
        },
        {
          field: "receiveTime",
          title: "收件时间",
          width: 150,
        },
        {
          field: "sender",
          title: "发件人",
          width: 150,
        },
        {
          field: "activityTypeName",
          title: "活动类别",
          width: 150,
        },
        {
          field: "receiveOrderStatusName",
          title: "接单状态",
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <span
                  style={{
                    color: this.getTagColor(row.receiveOrderStatusName),
                  }}
                >
                  {row.receiveOrderStatusName}
                </span>,
              ];
            },
          },
        },
        {
          field: "receiveOrderUserName",
          title: "配置接单人",
          width: 100,
        },
        {
          field: "receiveOrderTime",
          title: "配置接单时间",
          width: 100,
        },
        {
          field: "receiveOrderReviewName",
          title: "复核接单人",
          width: 100,
        },
        {
          field: "receiveOrderReviewTime",
          title: "复核接单时间",
          width: 100,
        },
        {
          field: "configStatusName",
          title: "配置状态",
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <span
                  style={{
                    color: this.getTagColor(row.configStatusName),
                  }}
                >
                  {row.configStatusName}
                </span>,
              ];
            },
          },
        },
        {
          field: "configUserName",
          title: "配置人",
          width: 100,
        },
        {
          field: "configTime",
          title: "配置时间",
          width: 100,
        },
        {
          field: "companyAttribute",
          title: "配置结果",
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <el-button
                  type="text"
                  size="large"
                  on={{
                    click: () => this.handleConfigResult(row),
                  }}
                >
                  查看
                </el-button>,
              ];
            },
          },
        },
        {
          field: "isReplyNew",
          title: "是否按新邮件回复",
          width: 100,
        },
        {
          field: "mailReplyContent",
          title: "邮件回复内容",
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <el-button
                  type="text"
                  size="large"
                  on={{
                    click: () =>
                      this.handleEmailDrawer(
                        row,
                        "mailReplyContent",
                        "邮件回复内容"
                      ),
                  }}
                >
                  查看
                </el-button>,
              ];
            },
          },
        },
        {
          field: "reviewerName",
          title: "复核人",
          width: 100,
        },
        {
          field: "reviewTime",
          title: "复核时间",
          width: 120,
        },
        {
          field: "handleGroupName",
          title: "复核结果",
          width: 120,
          slots: {
            default: ({ row }) => {
              return [
                <el-button
                  type="text"
                  size="large"
                  on={{
                    click: () => this.handleRecheckResult(row),
                  }}
                >
                  查看
                </el-button>,
              ];
            },
          },
        },
        {
          field: "remark",
          title: "备注",
          width: 120,
        },
        {
          field: "cancelTypeName",
          title: "作废类别",
          width: 150,
        },
        {
          field: "stationShareCount",
          title: "场站分润次数",
          width: 150,
        },
        {
          field: "workTimeTypeConfig",
          title: "工作时间类别--配置",
          width: 150,
        },
        {
          field: "workTimeTypeReview",
          title: "工作时间类别--复核",
          width: 100,
        },
        {
          field: "sendFlag",
          title: "是否已发送邮件",
          width: 100,
        },
        {
          field: "sendType",
          title: "邮件发送方式",
          width: 100,
        },
        {
          field: "sendTimes",
          title: "邮件发送次数",
          width: 100,
        },
        {
          field: "mailSender",
          title: "邮件发送人",
          width: 100,
        },
        {
          field: "sendTime",
          title: "邮件发送时间",
          width: 100,
        },
      ];
    },
    filterOptions() {
      let config = [
        // 默认显示的筛选条件
        {
          field: "obscureParams",
          element: "el-input",
          title: "模糊搜索",
          colSpan: { span: 16 },
          attrs: {
            placeholder: "请输入邮件标题/正文模糊搜索",
          },
        },
        {
          field: "receiveTime",
          title: "收件时间",
          element: "el-date-picker",
          props: {
            type: "daterange",
            valueFormat: "yyyy-MM-dd",
          },
        },
        {
          field: "activityType",
          title: "活动类别",
          element: "el-select",
          props: {
            options: this.activityTypeOptions,
            optionLabel: "dictLabel", //自定义选项名
            optionValue: "dictValue", //自定义选项值
            filterable: true,
          },
        },
        {
          field: "receiveOrderStatus",
          title: "接单状态",
          element: "el-select",
          props: {
            options: this.receiveOrderStatusOptions,
            optionLabel: "dictLabel", //自定义选项名
            optionValue: "dictValue", //自定义选项值
            filterable: true,
          },
        },
        {
          field: "receiveOrderUserName",
          title: "接单人",
        },
        {
          field: "receiveOrderTime",
          title: "接单时间",
          element: "el-date-picker",
          props: {
            type: "daterange",
            valueFormat: "yyyy-MM-dd",
          },
        },
        {
          field: "sender",
          title: "发件人",
        },
        {
          field: "configStatus",
          title: "配置状态",
          element: "el-select",
          props: {
            options: this.configStatusOptions,
            optionLabel: "dictLabel", //自定义选项名
            optionValue: "dictValue", //自定义选项值
            filterable: true,
          },
        },
        {
          field: "configUserName",
          title: "配置人",
        },
        {
          field: "configTime",
          title: "配置时间",
          element: "el-date-picker",
          props: {
            type: "daterange",
            valueFormat: "yyyy-MM-dd",
          },
        },
        {
          field: "sendFlag",
          title: "是否已发送邮件",
          element: "el-select",
          props: {
            options: [
              { value: "Y", label: "是" },
              { value: "N", label: "否" },
            ],
            filterable: true,
          },
        },
        {
          field: "sendType",
          title: "邮件发送方式",
          element: "el-select",
          props: {
            options: [
              { value: "01", label: "新建" },
              { value: "02", label: "转发" },
            ],
            filterable: true,
          },
        },
        {
          field: "mailSender",
          title: "发送人",
        },
        {
          field: "mailSource",
          title: "邮件来源",
          element: "el-select",
          props: {
            options: this.mailSourceOptions,
            filterable: true,
          },
        },
        {
          field: "reviewTime",
          title: "复核时间",
          element: "el-date-picker",
          props: {
            type: "daterange",
            valueFormat: "yyyy-MM-dd",
          },
        },
        {
          field: "sendTime",
          title: "发送时间",
          element: "el-date-picker",
          props: {
            type: "daterange",
            valueFormat: "yyyy-MM-dd",
          },
        },
      ];
      config.forEach((item) => {
        item.show = this.selectedFilterFields.includes(item.field);
      });
      return {
        showCount: 8, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: config,
        params: this.params,
      };
    },
    outBtnArr() {
      return this.modalConfig.customOperationTypes?.filter((x) => x.isOutside);
    },
    btnArr() {
      return this.modalConfig.customOperationTypes?.filter((x) => !x.isOutside);
    },
    modalConfig() {
      const form = {
        //接单
        accept: [
          {
            field: "receiveOrderType",
            element: "el-radio-group",
            title: "选择接单操作",
            props: {
              options: [
                { value: "1", label: "接单" },
                { value: "2", label: "作废" },
              ],
            },
            defaultValue: "1",
            rules: [{ required: true, message: "请选择接单操作" }],
          },
          {
            field: "cancelType",
            element: "el-select",
            title: "作废类别",
            props: {
              options: this.cancelTypeOptions,
              filterable: true,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
            rules: [
              {
                required:
                  this.$refs.crud?.getFormFields()?.receiveOrderType == "2",
                message: "请选择",
              },
            ],
          },
          {
            field: "cancelReason",
            element: "el-input",
            title: "作废原因",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "非必填，500个字符以内",
            },
          },
        ],
        //作废
        cancel: [
          {
            field: "cancelType",
            element: "el-select",
            title: "作废类别",
            props: {
              options: this.cancelTypeOptions,
              filterable: true,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
            rules: [
              {
                required: true,
                message: "请选择",
              },
            ],
          },
          {
            field: "cancelReason",
            element: "el-input",
            title: "作废原因",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "非必填，500个字符以内",
            },
          },
        ],
        //活动类别
        activityType: [
          {
            field: "activityType",
            element: "el-select",
            title: "选择活动类别",
            props: {
              options: this.activityTypeOptions,
              filterable: true,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
            rules: [
              {
                required: true,
                message: "请选择",
              },
            ],
          },
        ],
        //备注
        remark: [
          {
            field: "remark",
            element: "el-input",
            title: "备注信息",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "请输入具体的描述，500个字符以内",
            },
            rules: [{ required: true, message: "备注不能为空！" }],
          },
        ],
      };
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: true,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: form[this.operationType] || [],
        crudPermission: [],
        customOperationTypes: [
          {
            title: "发邮件",
            typeName: "replyEmail",
            isOutside: true,
            event: (row) => {
              return this.handleReplyEmail(row);
            },
            condition: () => {
              return checkPermission(["commonEmail:originalData:replyEmail"]);
            },
          },
          {
            title: "接单",
            typeName: "accept",
            isOutside: true,
            event: (row) => {
              return this.handleAccept(row);
            },
            condition: (row) => {
              //未接单/已作废
              const condition1 = ["1", "3"].includes(row.receiveOrderStatus);
              //已接单-配置+已配置
              const condition2 =
                row.receiveOrderStatus == "2" && row.configStatus == "2";
              return (
                (condition1 || condition2) &&
                checkPermission(["commonEmail:originalData:accept"])
              );
            },
          },
          {
            title: "配置",
            typeName: "config",
            slotName: "config",
            showForm: false,
            isOutside: true,
            event: (row) => {
              return this.handleConfig(row);
            },
            condition: (row) => {
              //已接单-配置+未配置
              const condition1 =
                row.receiveOrderStatus == "2" && row.configStatus == "1";
              //已接单-复核+复核驳回
              const condition2 =
                row.receiveOrderStatus == "4" && row.configStatus == "4";
              return (
                (condition1 || condition2) &&
                checkPermission(["commonEmail:originalData:config"])
              );
            },
          },
          {
            title: "复核",
            typeName: "recheck",
            isOutside: true,
            event: (row) => {
              return this.handleRecheck(row);
            },
            condition: (row) => {
              //已接单-复核+已配置
              const condition1 =
                row.receiveOrderStatus == "4" && row.configStatus == "2";

              return (
                condition1 &&
                checkPermission(["commonEmail:originalData:recheck"])
              );
            },
          },
          {
            title: "备注",
            typeName: "remark",
            // showForm: false,
            event: (row) => {
              return this.handleRemark(row);
            },
            condition: () => {
              return checkPermission(["commonEmail:originalData:remark"]);
            },
          },
          {
            title: "差异",
            typeName: "difference",
            event: (row) => {
              return this.handleDifference(row);
            },
            condition: (row) => {
              //已接单-复核+已复核
              const condition1 =
                row.receiveOrderStatus == "4" && row.configStatus == "3";

              return (
                condition1 &&
                checkPermission(["commonEmail:originalData:difference"])
              );
            },
          },
          {
            title: "作废",
            typeName: "cancel",
            event: (row) => {
              return this.handleCancel(row);
            },
            condition: (row) => {
              //已接单-复核/已接单-配置
              const condition1 =
                row.receiveOrderStatus == "2" || row.receiveOrderStatus == "4";
              return (
                condition1 &&
                checkPermission(["commonEmail:originalData:cancel"])
              );
            },
          },
          {
            title: "详情",
            typeName: "detail",
            event: (row) => {
              return this.jumpToDetail(row);
            },
            condition: () => {
              return checkPermission(["commonEmail:originalData:detail"]);
            },
          },
          //   {
          //     title: "活动类别",
          //     typeName: "activityType",
          //     event: (row) => {
          //       return this.handleActivityType(row);
          //     },
          //     condition: (row) => {
          //       //已接单
          //       const condition1 = row.receiveOrderStatus == "2";
          //       //未配置/已配置/复核驳回
          //       const condition2 = ["1", "2", "4"].includes(row.configStatus);
          //       return (
          //         condition1 &&
          //         condition2 &&
          //         checkPermission(["commonEmail:originalData:activityType"])
          //       );
          //     },
          //   },

          //非操作列按钮，占个弹窗
          {
            title: "附件",
            typeName: "file",
            slotName: "file",
            showForm: false,
            condition: () => {
              return false;
            },
          },
          //非操作列按钮，占个弹窗
          {
            title: "发件人签名",
            typeName: "sign",
            slotName: "sign",
            showForm: false,
            condition: () => {
              return false;
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "110px",
        },
      };
    },
  },
};
</script>

<style></style>
