/**
 * 表格列偏好设置Mixin
 * 提供表格列显示、隐藏、排序等偏好设置的通用功能
 *
 * 使用方式：
 * 1. 在组件中引入：import tablePreferenceMixin from "@/mixins/tablePreferenceMixin.js";
 * 2. 添加到mixins数组：mixins: [tablePreferenceMixin]
 * 3. 在tableProps中使用：customConfig: this.getTableCustomConfig("menuFlag", "tableId")
 * 4. 在模板中添加按钮：@click="handleCustom"，按钮文本：{{ tablePreference.isCustomColumn ? "已开启偏好设置" : "将自定义列设为偏好" }}
 */

import * as comApi from "@/api/common.js";

export default {
  data() {
    return {
      // 表格偏好设置相关数据
      tablePreference: {
        storeData: {}, // 存储表格偏好配置数据
        btnLoading: false, // 偏好设置按钮加载状态
        restorePromise: null, // 恢复偏好设置的Promise缓存
        isCustomColumn: false, // 是否已开启列偏好设置
        isPreferenceInitialized: false, // 标记表格偏好设置是否已初始化
      },
    };
  },
  methods: {
    /**
     * 获取表格自定义配置
     * @param {string} menuFlag - 页面唯一标识，用于区分不同页面的偏好设置
     * @param {string} tableId - 表格唯一ID
     * @returns {Object} customConfig配置对象
     */
    getTableCustomConfig(menuFlag, tableId) {
      const that = this;
      return {
        storage: true,
        checkMethod: ({ column }) => {
          // 可以添加列检查逻辑，返回false的列不允许自定义
          return true;
        },
        // 恢复偏好设置的方法
        restoreStore: () => {
          // 如果已经初始化过偏好设置，直接返回当前存储的数据
          // 这样可以确保用户调整的表格列配置不会被重置
          if (that.tablePreference.isPreferenceInitialized) {
            return that.tablePreference.storeData || {};
          }
          // 如果未发起过请求，则创建Promise（仅在页面初始化时执行）
          if (!that.tablePreference.restorePromise) {
            that.tablePreference.restorePromise = (async () => {
              try {
                const res = await comApi.queryCustomColumn({ menuFlag });
                that.tablePreference.isCustomColumn =
                  res.data.preferenceFlag === "Y";
                that.tablePreference.isPreferenceInitialized = true; // 标记已初始化
                // 根据服务端数据或默认值返回
                const preferenceData = that.tablePreference.isCustomColumn
                  ? JSON.parse(res.data.preferenceJson)
                  : {};
                // 保存偏好设置数据
                that.tablePreference.storeData = preferenceData;
                return preferenceData;
              } catch (e) {
                console.error("Failed to load table preferences:", e);
                that.tablePreference.isPreferenceInitialized = true; // 即使失败也标记为已初始化
                that.tablePreference.storeData = {}; // 确保有默认值
                return {}; // 失败返回空
              }
            })();
          }
          // 返回缓存的Promise
          return that.tablePreference.restorePromise;
        },
        // 更新偏好设置的方法
        updateStore({ id, type, storeData }) {
          return new Promise(async (resolve) => {
            that.tablePreference.storeData = storeData;
            if (that.tablePreference.isCustomColumn) {
              try {
                const res = await comApi.saveCustomColumn({
                  preferenceJson: JSON.stringify(storeData),
                  menuFlag,
                });
                if (res.code === "10000") {
                  that.$message.success("保存成功");
                }
              } catch (error) {
                console.error("Failed to save table preferences:", error);
              }
            }
            resolve();
          });
        },
      };
    },

    /**
     * 处理表格列偏好设置
     * @param {string} menuFlag - 页面唯一标识
     * @param {string} crudRef - BuseCrud组件的ref名称，默认为'crud'
     */
    async handleTableCustom(menuFlag, crudRef = "crud") {
      try {
        // 获取当前表格的列配置状态
        const currentStoreData = this.$refs[crudRef]
          .getVxeTableRef()
          ?.getCustomStoreData();

        const method = this.tablePreference.isCustomColumn
          ? "cancelCustomColumn"
          : "saveCustomColumn";
        this.tablePreference.btnLoading = true;

        const res = await comApi[method]({
          menuFlag,
          preferenceJson: JSON.stringify(currentStoreData),
        });

        this.tablePreference.btnLoading = false;
        const text = this.tablePreference.isCustomColumn
          ? "表格列偏好设置已取消"
          : "表格列偏好设置已开启";

        if (res.code === "10000") {
          this.tablePreference.isCustomColumn = !this.tablePreference
            .isCustomColumn;
          this.$message.success(text);
          this.tablePreference.restorePromise = null; // 清除缓存，下次重新获取

          // 无论开启还是取消偏好设置，都保存当前的表格列配置状态
          // 这样可以确保用户当前调整的表格列配置不会丢失
          this.tablePreference.storeData = currentStoreData;

          // 不重置 isPreferenceInitialized 标志，保持已初始化状态
          // 这样可以防止 restoreStore 重新从服务器获取旧的偏好设置
        }
      } catch (error) {
        console.error("Failed to handle table custom:", error);
        this.tablePreference.btnLoading = false;
        this.$message.error("操作失败，请重试");
      }
    },

    /**
     * 初始化表格列偏好设置
     * @param {string} menuFlag - 页面唯一标识
     */
    async initTablePreference(menuFlag) {
      try {
        const res = await comApi.queryCustomColumn({ menuFlag });
        if (res.code === "10000") {
          this.tablePreference.isCustomColumn = res.data.preferenceFlag === "Y";
          this.tablePreference.isPreferenceInitialized = true;
          if (this.tablePreference.isCustomColumn && res.data.preferenceJson) {
            this.tablePreference.storeData = JSON.parse(
              res.data.preferenceJson
            );
          }
        }
      } catch (error) {
        console.error("Failed to load table preferences:", error);
        this.tablePreference.isPreferenceInitialized = true; // 即使失败也标记为已初始化
      }
    },

    /**
     * 便捷方法：处理表格列偏好设置（使用默认参数）
     * 需要在组件中定义 MENU_FLAG 常量
     */
    async handleCustom() {
      if (!this.MENU_FLAG) {
        console.error("请在组件中定义 MENU_FLAG 常量");
        return;
      }
      return this.handleTableCustom(this.MENU_FLAG);
    },

    /**
     * 便捷方法：初始化表格列偏好设置（使用默认参数）
     * 需要在组件中定义 MENU_FLAG 常量
     */
    async initPreference() {
      if (!this.MENU_FLAG) {
        console.error("请在组件中定义 MENU_FLAG 常量");
        return;
      }
      return this.initTablePreference(this.MENU_FLAG);
    },
  },
};
