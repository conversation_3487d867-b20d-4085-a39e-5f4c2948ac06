<!-- 邮件配置结果 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['commonEmail:originalData:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            :loading="loading"
            >查询
          </el-button>
          <el-button
            icon="el-icon-refresh"
            @click.stop="handleReset"
            :loading="loading"
            >重置
          </el-button>
        </div>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/ledger/commonEmail.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { listAllUser, queryCityTree, listDept } from "@/api/common.js";

import "@riophae/vue-treeselect/dist/vue-treeselect.css";

import moment from "moment";
export default {
  name: "ledgerList",
  components: {},
  mixins: [exportMixin],
  data() {
    return {
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          // slots: {
          //   buttons: "toolbar_buttons",
          // },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "accept",
      //buse参数-e

      regionData: [],
      workTimeTypeOptions: [
        // { dictValue: "1", dictLabel: "节假日" },
        // { dictValue: "2", dictLabel: "工作日" },
        // { dictValue: "3", dictLabel: "休息日" },
      ],
      differenceTypeOptions: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    this.getDicts("difference_type").then((response) => {
      this.differenceTypeOptions = response.data;
    });
    this.getDicts("work_time_type").then((response) => {
      this.workTimeTypeOptions = response.data;
    });

    this.getCityRegionData();
    // this.loadData();
  },
  methods: {
    checkPermission,
    JumpToOrigin(row) {
      const params = { obscureParams: row.emailSubject };
      this.$emit("jump", params);
    },
    handleExport() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.handleCommonExport(api.exportResult, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "activityTime",
          title: "活动日期",
          startFieldName: "activityStartDate",
          endFieldName: "activityEndDate",
        },
        {
          field: "configTime",
          title: "配置时间",
          startFieldName: "startConfigTime",
          endFieldName: "endConfigTime",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    async loadData() {
      const { region } = this.params;
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      region?.[0] && (params.provinceCode = region[0] + "0000");
      region?.[1] && (params.cityCode = region[1] + "00");
      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.getResultList(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.orderTypeOptions = [];
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");
      // crudOperationType:accept/remark/cancel/activityType
      const res = await api[crudOperationType](params);
      if (res?.code === "10000") {
        this.$message.success("提交成功");
        this.loadData();
      } else {
        return false;
      }
    },
    getCityRegionData() {
      queryCityTree({}).then((res) => {
        this.regionData = this.cleanTree(res.data);
      });
    },
    cleanTree(arr) {
      return arr.map((item) => {
        // 复制当前对象
        const newItem = { ...item };

        // 处理子节点
        if (newItem.children) {
          // 递归处理子节点
          newItem.children = this.cleanTree(newItem.children);

          // 如果当前层级为 3 且子节点为空，删除 children 属性
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }

        return newItem;
      });
    },
    async remoteStationOptions(params, key) {
      const { searchText, ...rest } = params;
      const res = await api.getStationOptions({
        ...rest,
        [key]: searchText,
      });
      return {
        data: res.data,
        total: res.total,
      };
    },

    querySearch(queryString, cb, apiName) {
      api[apiName]({
        operator: queryString || "",
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x };
        });
        cb(result);
      });
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "stationName",
          title: "场站名称",
          width: 180,
        },
        {
          field: "stationCode",
          title: "场站编码",
          width: 120,
        },
        {
          field: "operator",
          title: "运营商名称",
          width: 120,
        },
        {
          field: "cityInfo",
          title: "城市",
          width: 120,
        },
        {
          field: "activityStartDate",
          title: "活动生效日期",
          width: 120,
        },
        {
          field: "activityEndDate",
          title: "活动结束日期",
          width: 120,
        },
        {
          field: "settlementRatio",
          title: "结算比例（%）",
          width: 120,
        },
        {
          field: "profitSharingIncome",
          title: "分润配置（分润收入）（%）",
          width: 120,
        },
        {
          field: "longTermAgreement",
          title: "长协（通用分润）（%）",
          width: 120,
        },
        // {
        //   field: "shortTermAgreement",
        //   title: "短协（活动分润）（%）",
        //   width: 120,
        // },
        {
          field: "workTimeTypeConfigName",
          title: "工作时间类别",
          width: 120,
        },
        {
          field: "recordStartTime",
          title: "开始时间",
          width: 120,
        },
        {
          field: "recordEndTime",
          title: "结束时间",
          width: 120,
        },
        {
          field: "expectedProfitSharing",
          title: "应收分润（%）",
          width: 120,
        },
        {
          field: "actualProfitSharing",
          title: "实际执行分润（%）",
          width: 120,
        },
        {
          field: "differenceAmount",
          title: "差异额（%）",
          width: 120,
        },
        {
          field: "differenceTypeName",
          title: "差异类型",
          width: 120,
        },
        {
          field: "reason",
          title: "原因",
          width: 120,
        },
        {
          field: "configTime",
          title: "配置时间",
          width: 120,
        },
        {
          field: "configUserName",
          title: "配置人",
          width: 120,
        },
        {
          field: "reviewTime",
          title: "复核时间",
          width: 120,
        },
        {
          field: "reviewerName",
          title: "复核人",
          width: 120,
        },
        {
          field: "emailSubject",
          title: "邮件标题",
          width: 180,
          slots: {
            default: ({ row }) => {
              return [
                <el-button
                  type="text"
                  size="large"
                  on={{
                    click: () => this.JumpToOrigin(row),
                  }}
                >
                  {row.emailSubject}
                </el-button>,
              ];
            },
          },
        },
        {
          field: "emailSendTime",
          title: "邮件回复时间",
          width: 100,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "emailSubject",
            element: "el-input",
            title: "邮件标题",
          },
          {
            field: "stationName",
            element: "page-autocomplete",
            title: "场站名称",
            props: {
              clearable: true,
              optionValue: "stationName",
              optionLabel: "stationName",
              fetchMethod: (params) => {
                return this.remoteStationOptions(params, "stationName");
              },
            },
          },
          {
            field: "stationCode",
            element: "page-autocomplete",
            title: "场站编码",
            props: {
              clearable: true,
              optionValue: "stationCode",
              optionLabel: "stationCode",
              fetchMethod: (params) => {
                return this.remoteStationOptions(params, "stationCode");
              },
            },
          },
          {
            field: "operator",
            title: "运营商名称",
            element: "el-autocomplete",
            props: {
              placeholder: "请输入或下拉选择",
              fetchSuggestions: (queryString, cb) => {
                return this.querySearch(queryString, cb, "getOperatorList");
              },
            },
          },
          {
            field: "activityTime",
            title: "活动日期",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "workTimeTypeConfig",
            title: "配置工作时间类别",
            element: "el-select",
            props: {
              options: this.workTimeTypeOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "differenceType",
            title: "差异类型",
            element: "el-select",
            props: {
              options: this.differenceTypeOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "configUserName",
            title: "配置人",
          },
          {
            field: "configTime",
            title: "配置时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "workTimeTypeReview",
            title: "复核工作时间类别",
            element: "el-select",
            props: {
              options: this.workTimeTypeOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "region",
            title: "省市",
            element: "custom-cascader",
            attrs: {
              collapseTags: true,
              props: {
                checkStrictly: false,
                multiple: false,
                value: "areaCode",
                label: "areaName",
              },
              options: this.regionData, //省市数据,
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [],
        crudPermission: [],
        customOperationTypes: [],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "110px",
        },
      };
    },
  },
};
</script>

<style></style>
