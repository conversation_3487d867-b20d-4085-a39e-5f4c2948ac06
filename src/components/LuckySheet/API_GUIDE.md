# LuckySheet组件API使用指南

## 概述

LuckySheet组件现在提供了两种数据获取方式：
1. **传统v-model方式**：适用于需要实时数据同步的场景
2. **主动API方式**：适用于在特定时机（如保存）主动获取数据的场景

## 新增API方法

### 1. getCurrentData()
主动获取当前表格的所有数据，不依赖v-model。

```javascript
// 获取当前表格数据
const data = this.$refs.luckySheet.getCurrentData();
if (data) {
  console.log('表格数据:', data);
  // 处理数据...
}
```

**返回值：**
- `Array`: LuckySheet的原始数据格式
- `null`: 获取失败或表格未初始化

### 2. validateData()
验证当前表格数据是否有效。

```javascript
// 验证数据
const validation = this.$refs.luckySheet.validateData();
if (validation.valid) {
  console.log('数据有效:', validation.data);
  // 继续处理...
} else {
  console.log('数据无效:', validation.message);
  this.$message.error(validation.message);
}
```

**返回值：**
```javascript
{
  valid: boolean,     // 数据是否有效
  message: string,    // 验证消息
  data?: Array       // 有效时返回数据
}
```

### 3. setData(data)
设置表格数据。

```javascript
// 设置新数据
const success = this.$refs.luckySheet.setData(newData);
if (success) {
  console.log('数据设置成功');
} else {
  console.log('数据设置失败');
}
```

**参数：**
- `data`: Array - LuckySheet格式的数据

**返回值：**
- `boolean`: 设置是否成功

### 4. clearData()
清空表格数据。

```javascript
// 清空数据
const success = this.$refs.luckySheet.clearData();
```

## 使用方式对比

### 方式一：传统v-model（保持兼容）

```vue
<template>
  <LuckySheet
    v-model="tableData"
    :container-id="'my-sheet'"
    ref="luckySheet"
  />
</template>

<script>
export default {
  data() {
    return {
      tableData: []
    }
  },
  methods: {
    handleSave() {
      // 使用v-model绑定的数据
      const data = this.tableData;
      this.saveToBackend(data);
    }
  }
}
</script>
```

### 方式二：主动API方式（推荐）

```vue
<template>
  <LuckySheet
    :container-id="'my-sheet'"
    ref="luckySheet"
  />
</template>

<script>
export default {
  methods: {
    handleSave() {
      // 主动获取最新数据
      const validation = this.$refs.luckySheet.validateData();
      if (!validation.valid) {
        this.$message.error(validation.message);
        return;
      }
      
      // 使用验证通过的数据
      this.saveToBackend(validation.data);
    },
    
    loadData(data) {
      // 设置数据
      this.$refs.luckySheet.setData(data);
    }
  }
}
</script>
```

## 最佳实践

### 1. 保存数据时的完整流程

```javascript
async handleSubmit() {
  // 1. 验证表单其他字段
  const formValid = await this.$refs.form.validate();
  if (!formValid) return;
  
  // 2. 验证表格数据
  const tableValidation = this.$refs.luckySheet.validateData();
  if (!tableValidation.valid) {
    this.$message.error(`表格数据错误: ${tableValidation.message}`);
    return;
  }
  
  // 3. 准备提交数据
  const submitData = {
    ...this.formData,
    tableData: tableValidation.data
  };
  
  // 4. 提交到后端
  try {
    await this.saveToBackend(submitData);
    this.$message.success('保存成功');
  } catch (error) {
    this.$message.error('保存失败');
  }
}
```

### 2. 数据加载时的处理

```javascript
async loadFormData(id) {
  try {
    const response = await this.getFromBackend(id);
    
    // 设置表单数据
    this.formData = response.formData;
    
    // 设置表格数据
    if (response.tableData) {
      this.$refs.luckySheet.setData(response.tableData);
    }
  } catch (error) {
    this.$message.error('数据加载失败');
  }
}
```

### 3. 错误处理

```javascript
methods: {
  getTableData() {
    const data = this.$refs.luckySheet.getCurrentData();
    if (!data) {
      console.error('无法获取表格数据，可能的原因：');
      console.error('1. 表格组件未初始化');
      console.error('2. LuckySheet库未加载');
      console.error('3. 组件ref引用错误');
      return null;
    }
    return data;
  }
}
```

## 迁移指南

### 从v-model迁移到API方式

1. **移除v-model绑定**：
```vue
<!-- 旧方式 -->
<LuckySheet v-model="tableData" ref="luckySheet" />

<!-- 新方式 -->
<LuckySheet ref="luckySheet" />
```

2. **修改数据获取逻辑**：
```javascript
// 旧方式
handleSave() {
  const data = this.tableData;
  this.saveToBackend(data);
}

// 新方式
handleSave() {
  const validation = this.$refs.luckySheet.validateData();
  if (validation.valid) {
    this.saveToBackend(validation.data);
  }
}
```

3. **修改数据设置逻辑**：
```javascript
// 旧方式
this.tableData = newData;

// 新方式
this.$refs.luckySheet.setData(newData);
```

## 注意事项

1. **组件引用**：确保正确设置ref属性
2. **异步操作**：在组件mounted后再调用API方法
3. **数据格式**：保持LuckySheet原生数据格式
4. **错误处理**：始终检查API方法的返回值
5. **性能考虑**：避免频繁调用getCurrentData()

## 兼容性

- ✅ 完全向后兼容v-model方式
- ✅ 支持同时使用两种方式
- ✅ 不影响现有功能
- ✅ 渐进式迁移
