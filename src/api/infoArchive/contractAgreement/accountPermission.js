import request from "@/utils/request";
//账号权限
// 列表查询
export function queryPermissionList(data) {
  return request({
    url: "/user/permission/getUserList",
    method: "get",
    params: data,
  });
}
//状态切换
export function changeStatus(data) {
  return request({
    url: "/user/permission/updateUserPermissionFlag",
    method: "post",
    data: data,
  });
}
//日志
export function queryLog(data) {
  return request({
    url: "group/manage/changeStatus",
    method: "post",
    data: data,
  });
}
//查询权限状态
export function getPermissionFlags(data) {
  return request({
    url: "/user/permission/getPermissionEnableFlag",
    method: "get",
    params: data,
  });
}

// 设置最高权限
export function changeRoot(data) {
  return request({
    url: "/user/permission/updateMaxEnableFlag",
    method: "post",
    data: data,
  });
}

//配置城市
//获取所有城市树
export function regionTreeList(data) {
  return request({
    url: "/user/permission/city/getCityTreeList",
    method: "get",
    params: data,
  });
}

//获取已配置城市
export function queryBindRegion(data) {
  return request({
    url: "user/permission/city/getBindCityTreeList",
    method: "get",
    params: data,
  });
}
//站点列表查询
export function queryStationList(data) {
  return request({
    url: "/user/permission/station/getBindStationList",
    method: "post",
    data: data,
  });
}
//已配置的站点
export function queryBindStation(data) {
  return request({
    url: "/user/permission/station/getBindStationInfo",
    method: "post",
    data: data,
  });
}
//保存站点
export function submitStation(data) {
  return request({
    url: "/user/permission/station/saveOrUpdate",
    method: "post",
    data: data,
  });
}
//一键配置
export function setAllConfig(data) {
  return request({
    url: "/user/permission/station/oneClick/save",
    method: "post",
    data: data,
  });
}

//一键清空
export function clearAllConfig(data) {
  return request({
    url: "/user/permission/station/oneClick/remove",
    method: "post",
    data: data,
  });
}
//配置能投大区
//获取所有能投大区
export function queryDeptTree(data) {
  return request({
    url: "/user/permission/org/getOrgTreeList",
    method: "get",
    params: data,
  });
}
//获取已配置能投大区
export function queryDeptChecked(data) {
  return request({
    url: "/user/permission/org/getBindOrgTreeList",
    method: "get",
    params: data,
  });
}
//保存能投大区配置
export function submitDept(data) {
  return request({
    url: "user/permission/org/saveOrUpdate",
    method: "post",
    data: data,
  });
}
//配置工单类型
//获取所有的工单类型
export function queryOrderTypeTree(data) {
  return request({
    url: "/user/permission/order/getOrderTypeTreeList",
    method: "get",
    params: data,
  });
}
//获取已配置工单类型
export function queryOrderChecked(data) {
  return request({
    url: "/user/permission/order/getBindOrderTypeTreeList",
    method: "get",
    params: data,
  });
}
//保存工单类型配置
export function submitOrder(data) {
  return request({
    url: "user/permission/order/saveOrUpdate",
    method: "post",
    data: data,
  });
}
