<template>
  <div class="app-container">
    <div class="page-title">
      <h3>编辑账号数据权限</h3>
      <el-button type="text" icon="el-icon-arrow-left" @click="goBack"
        >返回</el-button
      >
    </div>
    <div class="dialog-header">
      <div>{{ nickName + "-" + userName }}</div>
      <!-- <div>
        <el-switch
          v-model="permissionMaxEnableFlag"
          active-text="最高权限"
          @change="handleStatusChange"
          active-value="0"
          inactive-value="1"
        >
        </el-switch>
        <el-tooltip
          effect="dark"
          content="最高权限：指的是不受数据权限控制，用户在运维模块可查看所有的站点、工单类型、能投大区"
        >
          <i class="el-icon-question"></i>
        </el-tooltip>
      </div> -->
    </div>
    <el-radio-group v-model="activeName" style="margin:10px 0 20px;">
      <el-radio-button
        v-for="(item, index) in radioList"
        :key="index"
        :label="item.value"
        >{{ item.label }}</el-radio-button
      >
    </el-radio-group>
    <!-- <City v-show="activeName === 'city'" :userId="userId"></City> -->
    <!-- <Dept v-show="activeName === 'dept'" :userId="userId"></Dept> -->

    <Station
      v-show="activeName === 'station'"
      :userId="userId"
      :permissionFlag="permissionStationFlag"
    ></Station>
    <BusinessType
      v-show="activeName === 'business'"
      :userId="userId"
      :permissionFlag="permissionOrderTypeFlag"
    ></BusinessType>
  </div>
</template>

<script>
// import City from "./components/city.vue";
// import Dept from "./components/dept.vue";
import BusinessType from "./components/businessType.vue";
import Station from "./components/station.vue";
import {
  changeRoot,
  // getPermissionFlags,
} from "@/api/infoArchive/contractAgreement/accountPermission.js";
export default {
  name: "contractAccountPermissionEdit",
  components: {
    // City,
    // Dept,
    BusinessType,
    Station,
  },
  data() {
    return {
      radioList: [
        { label: "业务类型", value: "business" },
        { label: "站点", value: "station" },
        // { label: "城市", value: "city" },

        // { label: "能投大区", value: "dept" },
      ],
      activeName: "business",
      userId: "",
      nickName: "",
      userName: "",
      permissionMaxEnableFlag: "1",
      permissionStationFlag: "1",
      permissionOrderTypeFlag: "1",
    };
  },
  created() {
    this.userId = this.$route.query.userId || "";
    this.nickName = this.$route.query.nickName || "";
    this.userName = this.$route.query.userName || "";
    // this.getPermissionFlags();
  },
  methods: {
    // getPermissionFlags() {
    //   getPermissionFlags({ userId: this.userId }).then((res) => {
    //     const {
    //       permissionMaxEnableFlag,
    //       permissionStationFlag,
    //       permissionOrderTypeFlag,
    //     } = res.data;
    //     this.permissionMaxEnableFlag = permissionMaxEnableFlag;
    //     this.permissionStationFlag = permissionStationFlag;
    //     this.permissionOrderTypeFlag = permissionOrderTypeFlag;
    //   });
    // },
    goBack() {
      this.$router.push({
        name: "contractAccountPermission",
      });
    },
    async handleStatusChange(val) {
      const res = await changeRoot({
        userId: this.userId,
        permissionMaxEnableFlag: val,
      }).catch(() => {
        this.permissionMaxEnableFlag =
          this.permissionMaxEnableFlag == "0" ? "1" : "0";
      });
      if (res.code === "10000") {
        this.msgSuccess("操作成功");
      } else {
        this.msgError("操作失败");
        this.permissionMaxEnableFlag =
          this.permissionMaxEnableFlag == "0" ? "1" : "0";
      }
    },
  },
};
</script>

<style lang="less" scoped>
.page-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
/deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 10px 24px;
  font-size: 14px;
}
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 500;
}
</style>
