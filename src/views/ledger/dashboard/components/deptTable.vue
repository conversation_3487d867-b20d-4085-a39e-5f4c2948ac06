//部门汇总
<template>
  <div style="padding: 0 20px;">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @loadData="loadData"
      tabType="card"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['ledger:dashboard:deptExport']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            :loading="loading"
            >查询
          </el-button>
          <el-button
            icon="el-icon-refresh"
            @click.stop="handleReset"
            :loading="loading"
            >重置
          </el-button>
        </div>
      </template>
      <template #pageCenter>
        <div class="page-center">
          <div class="title">汇总：</div>
          <div>
            <el-tooltip
              effect="dark"
              placement="top"
              content="指的是以工单维度统计的个数"
            >
              <i class="el-icon-question ml5"></i>
            </el-tooltip>
            工单数量：<span class="count">{{ summary.orderCount || 0 }}</span
            ><span class="unit">个</span>
          </div>
          <div>
            <el-tooltip
              effect="dark"
              placement="top"
              content="指的是同一个工单被不同部门的人处理后，工单将分别计入每个部门、每个处理人名下，所以工单计算数量会大于工单数量"
            >
              <i class="el-icon-question ml5"></i>
            </el-tooltip>
            工单计算数量：<span class="count">{{
              summary.orderAllCount || 0
            }}</span
            ><span class="unit">个</span>
          </div>
          <div>
            工单总工时：<span class="count">{{
              summary.totalOrderTime || 0
            }}</span
            ><span class="unit">h</span>
          </div>
          <div>
            加单总工时：<span class="count">{{
              summary.totalAddTime || 0
            }}</span
            ><span class="unit">h</span>
          </div>
          <div>
            工时总和：<span class="count">{{ summary.totalTime || 0 }}</span
            ><span class="unit">h</span>
          </div>
          <div>
            工单价格：<span class="count">{{ summary.orderPrice || 0 }}</span
            ><span class="unit">元</span>
          </div>
        </div>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { initParams } from "@/utils/buse.js";
import listApi from "@/api/ledger/index.js";
import { queryBusinessTypeTree } from "@/api/ledger/businessType.js";
import { queryDeptOrderTree } from "@/api/ledger/workOrderType.js";
import { listAllUser, queryCityTree } from "@/api/common.js";

import api from "@/api/ledger/dashboard.js";
import comApi from "@/api/ledger/company.js";

import checkPermission from "@/utils/permission.js";
import exportMixin from "@/mixin/export.js";

export default {
  components: {},
  mixins: [exportMixin],
  props: {
    // searchParams: {
    //   type: Object,
    //   default: () => {},
    // },
  },
  data() {
    return {
      summary: {},
      addVisible: false, //添加站点
      editVisible: false, //忽略
      recordList: [],
      token: "",
      businessFirstLevelList: [],
      businessChildrenList: [],
      demandFirstLevelList: [],
      demandChildrenList: [],
      demandStatusOptions: [],
      //buse参数-s
      tabRadioList: [
        { value: "0", id: "0", label: "全部" },
        // { value: "1", id: "1", label: "进行中" },
      ],
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        // toolbarConfig: {
        //   custom: true,
        //   slots: {
        //     buttons: "toolbar_buttons",
        //   },
        // },
        rowConfig: {
          keyField: "projectBatchId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
        spanMethod: this.rowMergeMethod,
        // footerMethod: this.footerMethod,
        // showFooter: true,
      },
      tableData: [],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "transfer",
      //buse参数-e
      businessTypeOptions: [],
      supportDeptOptions: [],
      statusOptions: [],
      orderTypeOptions: [],
      processColumns: [],
    };
  },
  watch: {
    // searchParams: {
    //   handler(val) {
    //     this.params = { ...initParams(this.filterOptions.config), ...val };
    //   },
    // },
  },
  created() {
    this.token = getToken();
    this.params = initParams(this.filterOptions.config);
    this.listAllUser();
    queryDeptOrderTree({}).then((res) => {
      this.orderTypeOptions = res.data?.map((x) => {
        return { ...x };
      });
    });
    queryBusinessTypeTree({}).then((res) => {
      this.businessTypeOptions = res.data;
    });
  },
  mounted() {
    Promise.all([
      this.getDicts("support_dept").then((response) => {
        this.supportDeptOptions = response.data;
      }),
      //工单状态
      this.getDicts("ledger_order_status").then((response) => {
        this.statusOptions = response.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.loadData();
        });
      }, 500);
    });
  },
  computed: {
    modalConfig() {
      return {
        menu: true,
        addBtn: false,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        menuWidth: 160,
        menuFixed: "right",
        //自定义操作按钮
        customOperationTypes: [
          {
            title: "明细",
            typeName: "detail",
            event: (row) => {
              return this.jumpToDetail(row);
            },
            condition: (row) => {
              return checkPermission(["ledger:dashboard:deptDetail"]);
            },
          },
        ],
      };
    },
    tableColumn() {
      return [
        {
          field: "supportDeptName",
          title: "所属部门",
        },
        {
          field: "totalOrderCount",
          title: "工单数量汇总（个）",
        },
        {
          field: "totalOrderTimeSum",
          title: "工单总工时汇总（h）",
        },
        {
          field: "totalAddTimeSum",
          title: "加单总工时汇总（h）",
        },
        {
          field: "allTimeSum",
          title: "工时总和汇总（h）",
        },
        {
          field: "businessTypeStr",
          title: "业务类型",
        },
        {
          field: "orderTypeStr",
          title: "工单类型",
          slots: {
            default: ({ row }) => {
              return (
                <span style={row.handleRemindTag == "Y" ? "color:red" : ""}>
                  {row.orderTypeStr}
                </span>
              );
            },
          },
        },
        {
          field: "totalCount",
          title: "数量（个）",
          // slots: {
          //   default: ({ row }) => {
          //     return (
          //       <span style={row.handleRemindTag == "Y" ? "color:red" : ""}>
          //         {row.orderTypeStr}
          //       </span>
          //     );
          //   },
          // },
        },
        {
          field: "totalOrderTime",
          title: "工单总工时（h）",
          titlePrefix: {
            message: `指的是该类型的工单，所有工单的工单总工时之和`,
            icon: "vxe-icon-question-circle-fill",
          },
          // slots: {
          //   default: ({ row }) => {
          //     return (
          //       <span style={row.handleRemindTag == "Y" ? "color:red" : ""}>
          //         {row.totalTime}
          //       </span>
          //     );
          //   },
          // },
        },
        {
          field: "totalAddTime",
          title: "加单总工时（h）",
          titlePrefix: {
            message: `指的是该类型的工单，所有工单的加单总工时之和`,
            icon: "vxe-icon-question-circle-fill",
          },
          // slots: {
          //   default: ({ row }) => {
          //     return (
          //       <span style={row.handleRemindTag == "Y" ? "color:red" : ""}>
          //         {row.orderTypeStr}
          //       </span>
          //     );
          //   },
          // },
        },
        {
          field: "totalTime",
          title: "工时总和（h）",
          titlePrefix: {
            message: `工时总和=工单总工时+加单总工时`,
            icon: "vxe-icon-question-circle-fill",
          },
          // slots: {
          //   default: ({ row }) => {
          //     return (
          //       <span style={row.handleRemindTag == "Y" ? "color:red" : ""}>
          //         {row.orderTypeStr}
          //       </span>
          //     );
          //   },
          // },
        },
        {
          field: "orderPrice",
          title: "工单价格（元）",
          width: 100,
          titlePrefix: {
            message: `工单价格=该工单类型的工单价格总和`,
            icon: "vxe-icon-question-circle-fill",
          },
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "supportDept",
            title: "所属部门",
            element: "el-select",
            props: {
              options: this.supportDeptOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
              multiple: true,
              collapseTags: true,
            },
          },
          {
            field: "businessType",
            title: "业务类型",
            element: "el-cascader",
            // ref: "businessCascader",
            props: {
              // popperClass: "location",
              collapseTags: true,
              props: {
                // expandTrigger: "hover",
                checkStrictly: false,
                multiple: true,
                value: "id",
                label: "typeName",
                children: "childrenList",
              },
              options: this.businessTypeOptions,
            },
            on: {
              change: (val) => {
                this.handleBusinessChange(val);
              },
            },
          },
          {
            field: "orderTypeArr",
            title: "工单类型",
            element: "el-cascader",
            // ref: "orderCascader",
            props: {
              //  popperClass: "location",
              collapseTags: true,
              props: {
                // expandTrigger: "hover",
                checkStrictly: false,
                multiple: true,
                value: "id",
                label: "typeName",
                children: "childrenList",
              },
              options: this.orderTypeOptions,
            },
            on: {
              change: () => {
                // this.$refs.crud.$refs.autoFilters.$refs.orderCascader.dropDownVisible = false;
              },
            },
          },
          {
            field: "orderStatus",
            title: "工单状态",
            element: "el-select",
            props: {
              options: this.statusOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "submitTime",
            title: "提交日期",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
            defaultValue: [this.getFirstDayOfMonth(), this.getLastDayOfMonth()],
          },
          {
            field: "finishTime",
            title: "完成日期",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "handleTime",
            title: "节点处理时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "addTime",
            title: "加单完成时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
        ],
        params: this.params,
      };
    },
  },
  methods: {
    // 处理多选级联选择器数据
    processMultiCascaderData(cascaderData) {
      const result = {
        level1: [],
        level2: [],
        level3: [],
        level4: [],
      };

      // 如果数据为空，直接返回空结果
      if (!cascaderData) {
        return result;
      }

      // 处理旧数据格式（单选模式下可能是字符串或数字）
      if (!Array.isArray(cascaderData)) {
        // 如果是字符串或数字，将其视为第一级的选择
        result.level1.push(cascaderData);
        return result;
      }

      // 处理单个数组（非嵌套数组）的情况
      if (
        Array.isArray(cascaderData) &&
        cascaderData.length > 0 &&
        !Array.isArray(cascaderData[0])
      ) {
        // 如果是单个数组（如 [1, 2, 3]），将其视为一个完整的路径
        if (cascaderData.length >= 1) {
          result.level1.push(cascaderData[0]);
        }
        if (cascaderData.length >= 2) {
          result.level2.push(cascaderData[1]);
        }
        if (cascaderData.length >= 3) {
          result.level3.push(cascaderData[2]);
        }
        if (cascaderData.length >= 4) {
          result.level4.push(cascaderData[3]);
        }
        return result;
      }

      // 处理多选数据格式（嵌套数组）
      cascaderData.forEach((path) => {
        if (Array.isArray(path)) {
          // 根据路径长度确定层级
          if (path.length >= 1) {
            result.level1.push(path[0]);
          }
          if (path.length >= 2) {
            result.level2.push(path[1]);
          }
          if (path.length >= 3) {
            result.level3.push(path[2]);
          }
          if (path.length >= 4) {
            result.level4.push(path[3]);
          }
        }
      });

      return result;
    },

    footerMethod({ columns, data }) {
      const countedNames = new Set(); // 缓存已计算
      let sums = new Array(columns.length).fill(0);

      data.forEach((row) => {
        if (!countedNames.has(row.supportDeptName)) {
          countedNames.add(row.supportDeptName);
          // 只累加第2-5列（索引1-4）
          sums[1] += row.totalOrderCount; // 工单数量
          sums[2] += row.totalOrderTimeSum; // 总工时
          sums[3] += row.totalAddTimeSum; // 加单工时
          sums[4] += row.allTimeSum; // 总工时和
        }
      });

      return [
        [
          "合计", // 第1列
          sums[1],
          sums[2].toFixed(2),
          sums[3].toFixed(2),
          sums[4].toFixed(2),
        ],
      ];
    },
    handleBusinessChange(val) {
      // this.$refs.crud.$refs.autoFilters.$refs.businessCascader.dropDownVisible = false;
    },
    rowMergeMethod({ row, $rowIndex, column, data }) {
      const fields = [
        "supportDeptName",
        "totalOrderCount",
        "totalOrderTimeSum",
        "totalAddTimeSum",
        "allTimeSum",
      ];
      if (fields.includes(column.property)) {
        const prevRow = data[$rowIndex - 1];
        let nextRow = data[$rowIndex + 1];

        if (prevRow && prevRow.supportDeptName === row.supportDeptName) {
          return { rowspan: 0, colspan: 0 };
        } else {
          let rowspan = 1;
          while (nextRow && nextRow.supportDeptName === row.supportDeptName) {
            rowspan++;
            nextRow = data[$rowIndex + rowspan];
          }
          return { rowspan, colspan: 1 };
        }
      } else if (column.property === "businessTypeStr") {
        const prevRow = data[$rowIndex - 1];
        let nextRow = data[$rowIndex + 1];

        if (prevRow && prevRow.businessTypeStr === row.businessTypeStr) {
          return { rowspan: 0, colspan: 0 };
        } else {
          let rowspan = 1;
          while (nextRow && nextRow.businessTypeStr === row.businessTypeStr) {
            rowspan++;
            nextRow = data[$rowIndex + rowspan];
          }
          return { rowspan, colspan: 1 };
        }
      }
    },
    //跳转至数据明细tab
    jumpToDetail(row) {
      const {
        oneBusinessTypeId,
        twoBusinessTypeId,
        threeBusinessTypeId,
        oneOrderTypeId,
        twoOrderTypeId,
        threeOrderTypeId,
        supportDept,
      } = row;
      let params = {
        supportDept: [supportDept],
        businessType: [[]],
        orderTypeArr: [[]],
        orderStatus: this.params.orderStatus,
        submitTime: this.params.submitTime,
        finishTime: this.params.finishTime,
        // totalTime: row.totalTime,
      };
      oneBusinessTypeId && (params.businessType[0][0] = oneBusinessTypeId);
      twoBusinessTypeId && (params.businessType[0][1] = twoBusinessTypeId);
      threeBusinessTypeId && (params.businessType[0][2] = threeBusinessTypeId);
      supportDept && (params.orderTypeArr[0][0] = supportDept);
      oneOrderTypeId && (params.orderTypeArr[0][1] = oneOrderTypeId);
      twoOrderTypeId && (params.orderTypeArr[0][2] = twoOrderTypeId);
      threeOrderTypeId && (params.orderTypeArr[0][3] = threeOrderTypeId);
      this.$emit("jump", { tab: "detail", params });
    },

    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },

    querySearch(queryString, cb, api) {
      comApi[api]({
        name: queryString,
        companyName: queryString,
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x.companyName || x };
        });
        cb(result);
      });
    },

    checkPermission,
    listAllUser() {
      listAllUser({ status: "0" }).then((res) => {
        this.userOptions = res.data.map((x) => {
          return {
            ...x,
            value: x.userId,
            label: x.nickName + "-" + x.userName,
          };
        });
      });
    },

    getFirstDayOfMonth() {
      const today = new Date();
      today.setDate(1);
      return today.toISOString().split("T")[0];
    },
    getLastDayOfMonth() {
      const today = new Date();
      today.setMonth(today.getMonth() + 1);
      today.setDate(0);
      return today.toISOString().split("T")[0];
    },
    async loadData(searchParams) {
      if (searchParams) {
        this.params = {
          ...initParams(this.filterOptions.config),
          ...searchParams,
        };
      }
      const { businessType, orderTypeArr, supportDept, ...rest } = this.params;

      // 处理业务类型多选数据
      const businessTypeIds = this.processMultiCascaderData(businessType);

      // 处理工单类型多选数据
      const orderTypeIds = this.processMultiCascaderData(orderTypeArr);

      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...rest,
        oneBusinessTypeIds: businessTypeIds.level1 || [],
        twoBusinessTypeIds: businessTypeIds.level2 || [],
        threeBusinessTypeIds: businessTypeIds.level3 || [],
        oneOrderTypeIds: orderTypeIds.level2 || [],
        twoOrderTypeIds: orderTypeIds.level3 || [],
        threeOrderTypeIds: orderTypeIds.level4 || [],
        supportDepts: Array.isArray(supportDept)
          ? supportDept
          : supportDept
          ? [supportDept]
          : [],
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleTimeRange(params);
      console.log(params, "params");
      this.loading = true;
      const res = await api.getDeptTableData(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
      const res2 = await api.getDeptSummary(params);
      this.summary = res2.data;
    },
    handleExport() {
      const { businessType, orderTypeArr, supportDept, ...rest } = this.params;

      // 处理业务类型多选数据
      const businessTypeIds = this.processMultiCascaderData(businessType);

      // 处理工单类型多选数据
      const orderTypeIds = this.processMultiCascaderData(orderTypeArr);

      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...rest,
        oneBusinessTypeIds: businessTypeIds.level1 || [],
        twoBusinessTypeIds: businessTypeIds.level2 || [],
        threeBusinessTypeIds: businessTypeIds.level3 || [],
        oneOrderTypeIds: orderTypeIds.level2 || [],
        twoOrderTypeIds: orderTypeIds.level3 || [],
        threeOrderTypeIds: orderTypeIds.level4 || [],
        supportDepts: Array.isArray(supportDept)
          ? supportDept
          : supportDept
          ? [supportDept]
          : [],
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };
      this.handleTimeRange(params);
      this.handleCommonExport(api.exportDept, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "submitTime",
          title: "提交时间",
          startFieldName: "createStartTime",
          endFieldName: "createEndTime",
        },
        {
          field: "finishTime",
          title: "完成时间",
          startFieldName: "finishStartTime",
          endFieldName: "finishEndTime",
        },
        {
          field: "handleTime",
          title: "节点处理时间",
          startFieldName: "handleStartTime",
          endFieldName: "handleEndTime",
        },
        {
          field: "addTime",
          title: "加单完成时间",
          startFieldName: "startAddTime",
          endFieldName: "endAddTime",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/ .el-select-group__title {
    padding-left: 10px;
  }

  /deep/ .el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }

  /deep/ .el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
.timeline {
  &-title {
    font-size: 16px;
    font-weight: 500;
  }
}

.page-center {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 24px;
  background: #eff6f4;
  font-size: 14px;
  .title {
    font-size: 18px;
  }
  .count {
    font-size: 20px;
    color: red;
  }
  .unit {
    color: red;
  }
}
</style>
