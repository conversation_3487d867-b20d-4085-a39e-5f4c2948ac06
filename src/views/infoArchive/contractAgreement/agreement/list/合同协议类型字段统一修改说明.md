# 合同协议类型字段统一修改说明

## 修改概述

已成功将合同协议类型字段在新增/编辑页面中统一修改为`contractTypeName`，并确保该字段取字典数据的`dictLabel`值而不是`dictValue`。

## 具体修改内容

### 1. 主表单字段配置 (add.vue)

#### ✅ 字段配置更新
```javascript
{
  field: "contractTypeName",
  title: "合同协议类型",
  element: "el-select",
  props: {
    options: this.contractAgreementTypeOptions,
    optionValue: "dictLabel",  // 修改：使用dictLabel而不是dictValue
    optionLabel: "dictLabel",
    placeholder: "请选择合同协议类型",
    clearable: true,
  },
  // ...
}
```

#### ✅ 组件传递更新
```vue
<ContractOtherInfo
  ref="otherInfo"
  :contractTypeName="baseParams.contractTypeName"  // 修改：从contractType改为contractTypeName
  v-model="otherParams"
/>
```

### 2. 其他信息组件联动 (ContractOtherInfo.vue)

#### ✅ Props 更新
```javascript
props: {
  // 合同协议类型
  contractTypeName: {  // 修改：从contractType改为contractTypeName
    type: String,
    default: "",
  },
  // ...
},
```

#### ✅ 计算属性更新
```javascript
computed: {
  // 当前类型配置
  currentConfig() {
    return getConfigByType(this.contractTypeName);  // 修改：使用contractTypeName
  },
},
```

#### ✅ 监听器更新
```javascript
watch: {
  // 监听 contractTypeName 变化，重新初始化数据
  contractTypeName: {  // 修改：从contractType改为contractTypeName
    handler(newType, oldType) {
      if (newType !== oldType && newType) {
        this.initFormData();
      }
    },
    immediate: true,
  },
},
```

#### ✅ 方法更新
```javascript
initFormData() {
  // 如果没有合同类型，清空数据
  if (!this.contractTypeName) {  // 修改：使用contractTypeName
    // ...
  }
  
  // 如果配置无效，不进行初始化
  if (!config || config.name === "未知类型") {
    console.warn("Invalid config for contractTypeName:", this.contractTypeName);  // 修改：日志信息更新
    return;
  }
  // ...
}
```

### 3. 配置文件适配 (otherInfoConfig.js)

#### ✅ 配置键值映射更新
```javascript
export const CONTRACT_TYPE_CONFIG = {
  // 修改：使用字典标签作为键值，而不是数字ID
  "投建协议申请记录-充电": {
    name: "投建协议申请记录-充电",
    // ...
  },
  "投建协议申请记录-储能": {
    name: "投建协议申请记录-储能", 
    // ...
  },
  "合同申请记录": {
    name: "合同申请记录",
    // ...
  },
  "协议申请记录": {
    name: "协议申请记录",
    // ...
  },
  "采购框架协议申请": {
    name: "采购框架协议申请",
    // ...
  },
};
```

#### ✅ 兼容性支持
```javascript
// 数字ID到字典标签的映射（兼容性支持）
const ID_TO_LABEL_MAP = {
  "1": "投建协议申请记录-充电",
  "2": "投建协议申请记录-储能", 
  "3": "合同申请记录",
  "4": "协议申请记录",
  "5": "采购框架协议申请",
};

export function getConfigByType(contractType) {
  if (!contractType) {
    return { name: "未知类型", formFields: [], tableFields: [], displayFields: [] };
  }

  // 首先尝试直接匹配字典标签
  let config = CONTRACT_TYPE_CONFIG[contractType];
  
  // 如果没有找到，尝试通过数字ID映射
  if (!config && ID_TO_LABEL_MAP[contractType]) {
    const labelName = ID_TO_LABEL_MAP[contractType];
    config = CONTRACT_TYPE_CONFIG[labelName];
  }

  return config || { name: "未知类型", formFields: [], tableFields: [], displayFields: [] };
}
```

### 4. 数据传递和回显 (add.vue)

#### ✅ 编辑模式数据回显
```javascript
if (res?.code === "10000") {
  const data = res.data;
  // 回显基本信息
  this.baseParams = { ...this.baseParams, ...data };

  // 确保合同协议类型字段正确回显（使用字典标签）
  if (data.contractTypeName) {
    this.baseParams.contractTypeName = data.contractTypeName;
    console.log("编辑模式回显合同协议类型:", data.contractTypeName);
  }
  // ...
}
```

#### ✅ 提交数据验证
```javascript
// 验证合同协议类型字段
if (!params.contractTypeName) {
  this.$message.error("请选择合同协议类型");
  return;
}

console.log("提交的合同协议类型:", params.contractTypeName);
```

#### ✅ 运管申请单号自动填充
```javascript
// 自动填充相关字段
if (data.contractTypeName) {
  this.baseParams.contractTypeName = data.contractTypeName;
}

// 确保合同协议类型字段正确设置
console.log("自动填充的合同协议类型:", data.contractTypeName);
```

## 数据流程验证

### 1. 字典数据获取
```javascript
// 获取字典数据
this.getDicts("contract_type").then((response) => {
  this.contractAgreementTypeOptions = response.data;
  // 数据格式：[{dictLabel: "投建协议申请记录-充电", dictValue: "1"}, ...]
});
```

### 2. 字段值选择
- 用户选择时：选中的是`dictLabel`值（如："投建协议申请记录-充电"）
- 存储到`baseParams.contractTypeName`：字典标签值
- 传递给子组件：字典标签值

### 3. 配置匹配
- 子组件接收`contractTypeName`：字典标签值
- 配置查找：优先使用字典标签匹配，兼容数字ID
- 显示对应配置：基于匹配的配置显示表单项

### 4. 数据提交
- 提交数据中的`contractTypeName`：字典标签值
- 后端接收：字典标签值（符合接口文档要求）

## 测试要点

### 1. 新增功能测试
1. ✅ 选择合同协议类型下拉框显示字典标签
2. ✅ 选择后其他信息组件正确显示对应配置
3. ✅ 提交数据包含正确的字典标签值

### 2. 编辑功能测试
1. ✅ 数据回显时合同协议类型正确显示
2. ✅ 其他信息组件根据回显类型正确初始化
3. ✅ 修改类型后配置正确切换

### 3. 兼容性测试
1. ✅ 支持字典标签值匹配配置
2. ✅ 兼容历史数字ID值匹配
3. ✅ 未知类型时显示默认配置

### 4. 数据一致性测试
1. ✅ 前端显示值与提交值一致
2. ✅ 字段名称在各组件间保持一致
3. ✅ 配置映射关系正确

## 注意事项

1. **字段名一致性**：确保所有组件中都使用`contractTypeName`字段名
2. **字典值类型**：使用`dictLabel`而不是`dictValue`，确保语义化
3. **兼容性支持**：保持对历史数字ID的兼容，避免现有数据问题
4. **配置映射**：确保配置文件中的键值与字典标签完全匹配
5. **数据验证**：在提交前验证字段值的有效性

## 后续优化建议

1. 添加字典数据缓存机制，提高性能
2. 完善错误处理，处理字典数据加载失败的情况
3. 添加单元测试覆盖字段映射逻辑
4. 考虑添加字典数据变更的监听机制
